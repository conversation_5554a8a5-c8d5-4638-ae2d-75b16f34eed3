/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as AccountImport } from './routes/account'
import { Route as IndexImport } from './routes/index'
import { Route as AuthErrorImport } from './routes/auth/error'
import { Route as AdminPromoteImport } from './routes/admin/promote'
import { Route as AdminDashboardImport } from './routes/admin/dashboard'
import { Route as AdminBillingImport } from './routes/admin/billing'
import { Route as AccountBillingImport } from './routes/account/billing'
import { Route as AccountBillingCheckoutImport } from './routes/account/billing/checkout'
import { Route as AccountBillingCheckoutSessionIdImport } from './routes/account/billing/checkout/$sessionId'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AccountRoute = AccountImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AuthErrorRoute = AuthErrorImport.update({
  id: '/auth/error',
  path: '/auth/error',
  getParentRoute: () => rootRoute,
} as any)

const AdminPromoteRoute = AdminPromoteImport.update({
  id: '/admin/promote',
  path: '/admin/promote',
  getParentRoute: () => rootRoute,
} as any)

const AdminDashboardRoute = AdminDashboardImport.update({
  id: '/admin/dashboard',
  path: '/admin/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AdminBillingRoute = AdminBillingImport.update({
  id: '/admin/billing',
  path: '/admin/billing',
  getParentRoute: () => rootRoute,
} as any)

const AccountBillingRoute = AccountBillingImport.update({
  id: '/billing',
  path: '/billing',
  getParentRoute: () => AccountRoute,
} as any)

const AccountBillingCheckoutRoute = AccountBillingCheckoutImport.update({
  id: '/checkout',
  path: '/checkout',
  getParentRoute: () => AccountBillingRoute,
} as any)

const AccountBillingCheckoutSessionIdRoute =
  AccountBillingCheckoutSessionIdImport.update({
    id: '/$sessionId',
    path: '/$sessionId',
    getParentRoute: () => AccountBillingCheckoutRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/account': {
      id: '/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AccountImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/account/billing': {
      id: '/account/billing'
      path: '/billing'
      fullPath: '/account/billing'
      preLoaderRoute: typeof AccountBillingImport
      parentRoute: typeof AccountImport
    }
    '/admin/billing': {
      id: '/admin/billing'
      path: '/admin/billing'
      fullPath: '/admin/billing'
      preLoaderRoute: typeof AdminBillingImport
      parentRoute: typeof rootRoute
    }
    '/admin/dashboard': {
      id: '/admin/dashboard'
      path: '/admin/dashboard'
      fullPath: '/admin/dashboard'
      preLoaderRoute: typeof AdminDashboardImport
      parentRoute: typeof rootRoute
    }
    '/admin/promote': {
      id: '/admin/promote'
      path: '/admin/promote'
      fullPath: '/admin/promote'
      preLoaderRoute: typeof AdminPromoteImport
      parentRoute: typeof rootRoute
    }
    '/auth/error': {
      id: '/auth/error'
      path: '/auth/error'
      fullPath: '/auth/error'
      preLoaderRoute: typeof AuthErrorImport
      parentRoute: typeof rootRoute
    }
    '/account/billing/checkout': {
      id: '/account/billing/checkout'
      path: '/checkout'
      fullPath: '/account/billing/checkout'
      preLoaderRoute: typeof AccountBillingCheckoutImport
      parentRoute: typeof AccountBillingImport
    }
    '/account/billing/checkout/$sessionId': {
      id: '/account/billing/checkout/$sessionId'
      path: '/$sessionId'
      fullPath: '/account/billing/checkout/$sessionId'
      preLoaderRoute: typeof AccountBillingCheckoutSessionIdImport
      parentRoute: typeof AccountBillingCheckoutImport
    }
  }
}

// Create and export the route tree

interface AccountBillingCheckoutRouteChildren {
  AccountBillingCheckoutSessionIdRoute: typeof AccountBillingCheckoutSessionIdRoute
}

const AccountBillingCheckoutRouteChildren: AccountBillingCheckoutRouteChildren =
  {
    AccountBillingCheckoutSessionIdRoute: AccountBillingCheckoutSessionIdRoute,
  }

const AccountBillingCheckoutRouteWithChildren =
  AccountBillingCheckoutRoute._addFileChildren(
    AccountBillingCheckoutRouteChildren,
  )

interface AccountBillingRouteChildren {
  AccountBillingCheckoutRoute: typeof AccountBillingCheckoutRouteWithChildren
}

const AccountBillingRouteChildren: AccountBillingRouteChildren = {
  AccountBillingCheckoutRoute: AccountBillingCheckoutRouteWithChildren,
}

const AccountBillingRouteWithChildren = AccountBillingRoute._addFileChildren(
  AccountBillingRouteChildren,
)

interface AccountRouteChildren {
  AccountBillingRoute: typeof AccountBillingRouteWithChildren
}

const AccountRouteChildren: AccountRouteChildren = {
  AccountBillingRoute: AccountBillingRouteWithChildren,
}

const AccountRouteWithChildren =
  AccountRoute._addFileChildren(AccountRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/account': typeof AccountRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/account/billing': typeof AccountBillingRouteWithChildren
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/admin/promote': typeof AdminPromoteRoute
  '/auth/error': typeof AuthErrorRoute
  '/account/billing/checkout': typeof AccountBillingCheckoutRouteWithChildren
  '/account/billing/checkout/$sessionId': typeof AccountBillingCheckoutSessionIdRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/account': typeof AccountRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/account/billing': typeof AccountBillingRouteWithChildren
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/admin/promote': typeof AdminPromoteRoute
  '/auth/error': typeof AuthErrorRoute
  '/account/billing/checkout': typeof AccountBillingCheckoutRouteWithChildren
  '/account/billing/checkout/$sessionId': typeof AccountBillingCheckoutSessionIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/account': typeof AccountRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/account/billing': typeof AccountBillingRouteWithChildren
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/admin/promote': typeof AdminPromoteRoute
  '/auth/error': typeof AuthErrorRoute
  '/account/billing/checkout': typeof AccountBillingCheckoutRouteWithChildren
  '/account/billing/checkout/$sessionId': typeof AccountBillingCheckoutSessionIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/account'
    | '/dashboard'
    | '/login'
    | '/account/billing'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/admin/promote'
    | '/auth/error'
    | '/account/billing/checkout'
    | '/account/billing/checkout/$sessionId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/account'
    | '/dashboard'
    | '/login'
    | '/account/billing'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/admin/promote'
    | '/auth/error'
    | '/account/billing/checkout'
    | '/account/billing/checkout/$sessionId'
  id:
    | '__root__'
    | '/'
    | '/account'
    | '/dashboard'
    | '/login'
    | '/account/billing'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/admin/promote'
    | '/auth/error'
    | '/account/billing/checkout'
    | '/account/billing/checkout/$sessionId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AccountRoute: typeof AccountRouteWithChildren
  DashboardRoute: typeof DashboardRoute
  LoginRoute: typeof LoginRoute
  AdminBillingRoute: typeof AdminBillingRoute
  AdminDashboardRoute: typeof AdminDashboardRoute
  AdminPromoteRoute: typeof AdminPromoteRoute
  AuthErrorRoute: typeof AuthErrorRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AccountRoute: AccountRouteWithChildren,
  DashboardRoute: DashboardRoute,
  LoginRoute: LoginRoute,
  AdminBillingRoute: AdminBillingRoute,
  AdminDashboardRoute: AdminDashboardRoute,
  AdminPromoteRoute: AdminPromoteRoute,
  AuthErrorRoute: AuthErrorRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/account",
        "/dashboard",
        "/login",
        "/admin/billing",
        "/admin/dashboard",
        "/admin/promote",
        "/auth/error"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/account": {
      "filePath": "account.tsx",
      "children": [
        "/account/billing"
      ]
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/account/billing": {
      "filePath": "account/billing.tsx",
      "parent": "/account",
      "children": [
        "/account/billing/checkout"
      ]
    },
    "/admin/billing": {
      "filePath": "admin/billing.tsx"
    },
    "/admin/dashboard": {
      "filePath": "admin/dashboard.tsx"
    },
    "/admin/promote": {
      "filePath": "admin/promote.tsx"
    },
    "/auth/error": {
      "filePath": "auth/error.tsx"
    },
    "/account/billing/checkout": {
      "filePath": "account/billing/checkout.tsx",
      "parent": "/account/billing",
      "children": [
        "/account/billing/checkout/$sessionId"
      ]
    },
    "/account/billing/checkout/$sessionId": {
      "filePath": "account/billing/checkout/$sessionId.tsx",
      "parent": "/account/billing/checkout"
    }
  }
}
ROUTE_MANIFEST_END */

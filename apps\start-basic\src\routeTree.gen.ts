/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as IndexImport } from './routes/index'
import { Route as BillingIndexImport } from './routes/billing/index'
import { Route as AuthErrorImport } from './routes/auth/error'
import { Route as AdminDashboardImport } from './routes/admin/dashboard'
import { Route as AdminBillingImport } from './routes/admin/billing'
import { Route as BillingCheckoutSessionIdImport } from './routes/billing/checkout/$sessionId'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const BillingIndexRoute = BillingIndexImport.update({
  id: '/billing/',
  path: '/billing/',
  getParentRoute: () => rootRoute,
} as any)

const AuthErrorRoute = AuthErrorImport.update({
  id: '/auth/error',
  path: '/auth/error',
  getParentRoute: () => rootRoute,
} as any)

const AdminDashboardRoute = AdminDashboardImport.update({
  id: '/admin/dashboard',
  path: '/admin/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AdminBillingRoute = AdminBillingImport.update({
  id: '/admin/billing',
  path: '/admin/billing',
  getParentRoute: () => rootRoute,
} as any)

const BillingCheckoutSessionIdRoute = BillingCheckoutSessionIdImport.update({
  id: '/billing/checkout/$sessionId',
  path: '/billing/checkout/$sessionId',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/admin/billing': {
      id: '/admin/billing'
      path: '/admin/billing'
      fullPath: '/admin/billing'
      preLoaderRoute: typeof AdminBillingImport
      parentRoute: typeof rootRoute
    }
    '/admin/dashboard': {
      id: '/admin/dashboard'
      path: '/admin/dashboard'
      fullPath: '/admin/dashboard'
      preLoaderRoute: typeof AdminDashboardImport
      parentRoute: typeof rootRoute
    }
    '/auth/error': {
      id: '/auth/error'
      path: '/auth/error'
      fullPath: '/auth/error'
      preLoaderRoute: typeof AuthErrorImport
      parentRoute: typeof rootRoute
    }
    '/billing/': {
      id: '/billing/'
      path: '/billing'
      fullPath: '/billing'
      preLoaderRoute: typeof BillingIndexImport
      parentRoute: typeof rootRoute
    }
    '/billing/checkout/$sessionId': {
      id: '/billing/checkout/$sessionId'
      path: '/billing/checkout/$sessionId'
      fullPath: '/billing/checkout/$sessionId'
      preLoaderRoute: typeof BillingCheckoutSessionIdImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/auth/error': typeof AuthErrorRoute
  '/billing': typeof BillingIndexRoute
  '/billing/checkout/$sessionId': typeof BillingCheckoutSessionIdRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/auth/error': typeof AuthErrorRoute
  '/billing': typeof BillingIndexRoute
  '/billing/checkout/$sessionId': typeof BillingCheckoutSessionIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/auth/error': typeof AuthErrorRoute
  '/billing/': typeof BillingIndexRoute
  '/billing/checkout/$sessionId': typeof BillingCheckoutSessionIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/login'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/auth/error'
    | '/billing'
    | '/billing/checkout/$sessionId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/dashboard'
    | '/login'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/auth/error'
    | '/billing'
    | '/billing/checkout/$sessionId'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/login'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/auth/error'
    | '/billing/'
    | '/billing/checkout/$sessionId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRoute
  LoginRoute: typeof LoginRoute
  AdminBillingRoute: typeof AdminBillingRoute
  AdminDashboardRoute: typeof AdminDashboardRoute
  AuthErrorRoute: typeof AuthErrorRoute
  BillingIndexRoute: typeof BillingIndexRoute
  BillingCheckoutSessionIdRoute: typeof BillingCheckoutSessionIdRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRoute,
  LoginRoute: LoginRoute,
  AdminBillingRoute: AdminBillingRoute,
  AdminDashboardRoute: AdminDashboardRoute,
  AuthErrorRoute: AuthErrorRoute,
  BillingIndexRoute: BillingIndexRoute,
  BillingCheckoutSessionIdRoute: BillingCheckoutSessionIdRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/dashboard",
        "/login",
        "/admin/billing",
        "/admin/dashboard",
        "/auth/error",
        "/billing/",
        "/billing/checkout/$sessionId"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/admin/billing": {
      "filePath": "admin/billing.tsx"
    },
    "/admin/dashboard": {
      "filePath": "admin/dashboard.tsx"
    },
    "/auth/error": {
      "filePath": "auth/error.tsx"
    },
    "/billing/": {
      "filePath": "billing/index.tsx"
    },
    "/billing/checkout/$sessionId": {
      "filePath": "billing/checkout/$sessionId.tsx"
    }
  }
}
ROUTE_MANIFEST_END */

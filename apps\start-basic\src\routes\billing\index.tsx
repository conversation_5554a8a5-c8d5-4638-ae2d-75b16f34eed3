// apps/start-basic/src/routes/billing/index.tsx
import { createFileRoute, redirect } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { useState } from 'react'
import { auth } from '@/lib/auth'

const getSession = createServerFn({
  method: 'GET',
}).handler(async (_, { request }) => {
  const session = await auth.api.getSession({
    headers: request.headers,
  })
  return session
})

export const Route = createFileRoute('/billing/')({
  beforeLoad: async () => {
    const session = await getSession()

    // Redirect non-admin users to dashboard
    if (!session?.user || session.user.role !== 'admin') {
      throw redirect({ to: '/dashboard' })
    }
  },
  component: BillingDashboard,
})

function BillingDashboard() {
  const [selectedTab, setSelectedTab] = useState<'products' | 'subscriptions' | 'events'>('products')

  // TODO: Add Convex queries once types are generated
  const products: any[] = [] // Placeholder
  // const products = useQuery(api.billing.getProducts)
  // const subscriptions = useQuery(api.subscriptions.getUserSubscription, { userId: 'admin' })
  // const billingEvents = useQuery(api.billing.getUserBillingHistory, { userId: 'admin' })

  // TODO: Add mutations once types are generated
  // const upsertProduct = useMutation(api.billing.upsertProduct)

  const [newProduct, setNewProduct] = useState({
    id: '',
    name: '',
    description: '',
    price: 0,
    currency: 'USD',
    interval: 'month' as 'month' | 'year',
    features: [''],
    active: true,
  })

  const handleCreateProduct = async () => {
    try {
      // TODO: Implement product creation once mutations are available
      console.log('Creating product:', newProduct)
      alert('Product creation will be implemented once Convex types are generated')

      // Reset form
      setNewProduct({
        id: '',
        name: '',
        description: '',
        price: 0,
        currency: 'USD',
        interval: 'month',
        features: [''],
        active: true,
      })
    } catch (error) {
      console.error('Error creating product:', error)
    }
  }

  const addFeature = () => {
    setNewProduct(prev => ({
      ...prev,
      features: [...prev.features, '']
    }))
  }

  const updateFeature = (index: number, value: string) => {
    setNewProduct(prev => ({
      ...prev,
      features: prev.features.map((f, i) => i === index ? value : f)
    }))
  }

  const removeFeature = (index: number) => {
    setNewProduct(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Billing Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage products, subscriptions, and billing events</p>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'products', label: 'Products' },
              { key: 'subscriptions', label: 'Subscriptions' },
              { key: 'events', label: 'Billing Events' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setSelectedTab(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Products Tab */}
        {selectedTab === 'products' && (
          <div className="space-y-8">
            {/* Create Product Form */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Create New Product</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Product ID</label>
                  <input
                    type="text"
                    value={newProduct.id}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, id: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="premium-monthly"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  <input
                    type="text"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="Premium Plan"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    value={newProduct.description}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                    placeholder="Full access to all premium features"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
                  <input
                    type="number"
                    value={newProduct.price}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, price: Number(e.target.value) }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="9.99"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Interval</label>
                  <select
                    value={newProduct.interval}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, interval: e.target.value as 'month' | 'year' }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="month">Monthly</option>
                    <option value="year">Yearly</option>
                  </select>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Features</label>
                  {newProduct.features.map((feature, index) => (
                    <div key={index} className="flex gap-2 mb-2">
                      <input
                        type="text"
                        value={feature}
                        onChange={(e) => updateFeature(index, e.target.value)}
                        className="flex-1 border border-gray-300 rounded-md px-3 py-2"
                        placeholder="Feature description"
                      />
                      <button
                        onClick={() => removeFeature(index)}
                        className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={addFeature}
                    className="mt-2 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                  >
                    Add Feature
                  </button>
                </div>
              </div>
              <div className="mt-6">
                <button
                  onClick={handleCreateProduct}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create Product
                </button>
              </div>
            </div>

            {/* Existing Products */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Existing Products</h2>
              </div>
              <div className="divide-y divide-gray-200">
                {products.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    No products found. Create your first product above.
                  </div>
                ) : (
                  products.map((product: any) => (
                    <div key={product._id} className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{product.name}</h3>
                          <p className="text-gray-600 mt-1">{product.description}</p>
                          <div className="mt-2">
                            <span className="text-2xl font-bold text-blue-600">
                              ${product.price}/{product.interval}
                            </span>
                            <span className={`ml-3 px-2 py-1 text-xs rounded-full ${
                              product.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {product.active ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          <div className="mt-3">
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Features:</h4>
                            <ul className="text-sm text-gray-600 space-y-1">
                              {product.features.map((feature: string, index: number) => (
                                <li key={index}>• {feature}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {/* Subscriptions Tab */}
        {selectedTab === 'subscriptions' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Subscription Management</h2>
            <p className="text-gray-600">Subscription management features coming soon...</p>
          </div>
        )}

        {/* Billing Events Tab */}
        {selectedTab === 'events' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Billing Events</h2>
            <p className="text-gray-600">Billing events history coming soon...</p>
          </div>
        )}
      </div>
    </div>
  )
}

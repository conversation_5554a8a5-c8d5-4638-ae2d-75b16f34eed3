// apps/start-basic/src/routes/account/billing/checkout/$sessionId.tsx
import { createFileRoute, useRouter } from '@tanstack/react-router'
import { useQuery, useMutation } from 'convex/react'
import { api } from '../../../../../../../convex/_generated/api'
import { useEffect, useState } from 'react'
import { Autumn } from 'autumn-js'

export const Route = createFileRoute('/account/billing/checkout/$sessionId')({
  component: CheckoutPage,
})

function CheckoutPage() {
  const { sessionId } = Route.useParams()
  const router = useRouter()
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get checkout session details
  const session = useQuery(api.billing.getCheckoutSession, { sessionId })
  const updateSession = useMutation(api.billing.updateCheckoutSession)
  const updateSubscription = useMutation(api.subscriptions.updateSubscription)

  // Get product details
  const products = useQuery(api.billing.getProducts)
  const product = products?.find(p => p.id === session?.productId)

  useEffect(() => {
    if (session?.status === 'expired') {
      setError('This checkout session has expired. Please try again.')
    } else if (session?.status === 'completed') {
      // Redirect to success page
      window.location.href = session.successUrl
    }
  }, [session])

  const handlePayment = async () => {
    if (!session || !product) return

    setIsProcessing(true)
    setError(null)

    try {
      // Initialize Autumn
      const autumn = new Autumn({
        publishableKey: process.env.VITE_AUTUMN_PUBLISHABLE_KEY || '',
        // Configure for your environment
      })

      // Create Autumn checkout session
      const autumnSession = await autumn.createCheckoutSession({
        productId: product.id,
        customerEmail: session.userId, // You might want to get actual email
        successUrl: session.successUrl,
        cancelUrl: session.cancelUrl,
        metadata: {
          convexSessionId: sessionId,
          userId: session.userId,
        },
      })

      // Update our session with Autumn session info
      await updateSession({
        sessionId,
        status: 'completed',
        metadata: { autumnSessionId: autumnSession.id },
      })

      // For demo purposes, simulate successful payment
      // In real implementation, this would be handled by webhooks
      setTimeout(async () => {
        try {
          // Create subscription record
          await updateSubscription({
            userId: session.userId,
            platform: 'stripe',
            externalId: `autumn_${autumnSession.id}`,
            productId: product.id,
            status: 'active',
            currentPeriodStart: Date.now(),
            currentPeriodEnd: Date.now() + (product.interval === 'month' ? 30 : 365) * 24 * 60 * 60 * 1000,
          })

          // Redirect to success URL
          window.location.href = session.successUrl
        } catch (err) {
          console.error('Error creating subscription:', err)
          setError('Payment succeeded but there was an error setting up your subscription. Please contact support.')
        }
      }, 2000)

    } catch (err) {
      console.error('Payment error:', err)
      setError('Payment failed. Please try again.')
      setIsProcessing(false)
    }
  }

  const handleCancel = () => {
    if (session?.cancelUrl) {
      window.location.href = session.cancelUrl
    } else {
      router.history.back()
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loading...</h1>
        </div>
      </div>
    )
  }

  if (session.status === 'expired') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Session Expired</h1>
          <p className="text-gray-600 mb-6">This checkout session has expired. Please try again.</p>
          <button
            onClick={handleCancel}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Complete Your Purchase
          </h1>

          {product && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg">
              <h2 className="text-lg font-semibold text-gray-800 mb-2">
                {product.name}
              </h2>
              <p className="text-gray-600 mb-3">{product.description}</p>
              <div className="text-2xl font-bold text-blue-600 mb-3">
                ${product.price}/{product.interval}
              </div>
              <div className="space-y-1">
                {product.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm text-gray-700">
                    <span className="text-green-500 mr-2">✓</span>
                    {feature}
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-3">
            <button
              onClick={handlePayment}
              disabled={isProcessing}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isProcessing ? 'Processing...' : `Pay $${product?.price}`}
            </button>

            <button
              onClick={handleCancel}
              disabled={isProcessing}
              className="w-full bg-gray-200 text-gray-800 py-3 px-4 rounded-md hover:bg-gray-300 disabled:opacity-50 transition-colors"
            >
              Cancel
            </button>
          </div>

          <p className="text-xs text-gray-500 text-center mt-4">
            Secure payment powered by Autumn + Stripe
          </p>
        </div>
      </div>
    </div>
  )
}

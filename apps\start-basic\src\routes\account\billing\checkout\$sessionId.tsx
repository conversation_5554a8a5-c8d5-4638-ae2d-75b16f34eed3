import { createFileRoute, useRouter } from '@tanstack/react-router'
import { useState } from 'react'
import { useAutumn } from 'autumn-js/react'

export const Route = createFileRoute('/account/billing/checkout/$sessionId')({
  component: CheckoutPage,
})

function CheckoutPage() {
  const { sessionId } = Route.useParams()
  const router = useRouter()
  const { attach } = useAutumn()
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Mock session data for now - in production this would come from your backend
  const session = {
    id: sessionId,
    status: 'pending',
    productId: 'premium',
    successUrl: '/account/billing?success=true',
    cancelUrl: '/account/billing?cancelled=true'
  }

  // Mock product data - in production this would come from Autumn
  const product = {
    id: 'premium',
    name: 'Premium Plan',
    description: 'Unlimited messages and premium features',
    price: 9.99,
    interval: 'month',
    features: ['Unlimited messages', 'Priority support', 'Advanced features']
  }

  const handlePayment = async () => {
    if (!session || !product) return

    setIsProcessing(true)
    setError(null)

    try {
      // Use Autumn's attach function to handle the payment
      await attach({ productId: product.id })

      // Redirect to success URL after successful payment
      window.location.href = session.successUrl

    } catch (err) {
      console.error('Payment error:', err)
      setError('Payment failed. Please try again.')
      setIsProcessing(false)
    }
  }

  const handleCancel = () => {
    if (session?.cancelUrl) {
      window.location.href = session.cancelUrl
    } else {
      router.history.back()
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loading...</h1>
        </div>
      </div>
    )
  }

  if (session.status === 'expired') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Session Expired</h1>
          <p className="text-gray-600 mb-6">This checkout session has expired. Please try again.</p>
          <button
            onClick={handleCancel}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Complete Your Purchase
          </h1>

          {product && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg">
              <h2 className="text-lg font-semibold text-gray-800 mb-2">
                {product.name}
              </h2>
              <p className="text-gray-600 mb-3">{product.description}</p>
              <div className="text-2xl font-bold text-blue-600 mb-3">
                ${product.price}/{product.interval}
              </div>
              <div className="space-y-1">
                {product.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm text-gray-700">
                    <span className="text-green-500 mr-2">✓</span>
                    {feature}
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-3">
            <button
              onClick={handlePayment}
              disabled={isProcessing}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isProcessing ? 'Processing...' : `Pay $${product?.price}`}
            </button>

            <button
              onClick={handleCancel}
              disabled={isProcessing}
              className="w-full bg-gray-200 text-gray-800 py-3 px-4 rounded-md hover:bg-gray-300 disabled:opacity-50 transition-colors"
            >
              Cancel
            </button>
          </div>

          <p className="text-xs text-gray-500 text-center mt-4">
            Secure payment powered by Autumn + Stripe
          </p>
        </div>
      </div>
    </div>
  )
}

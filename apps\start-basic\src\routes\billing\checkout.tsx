import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useState } from 'react'
import { useSession } from '@/lib/auth-client'

export const Route = createFileRoute('/billing/checkout')({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      plan: (search.plan as string) || 'premium-monthly',
    }
  },
  component: CheckoutPage,
})

function CheckoutPage() {
  const { plan } = useSearch({ from: '/billing/checkout' })
  const [isLoading, setIsLoading] = useState(false)

  // Plan configurations
  const plans = {
    'premium-monthly': {
      name: 'Premium Plan',
      price: 9.99,
      interval: 'month',
      features: ['Unlimited messages', 'Priority support', 'Advanced features']
    },
    'pro-monthly': {
      name: 'Pro Plan',
      price: 19.99,
      interval: 'month',
      features: ['Everything in Premium', 'Team collaboration', 'Analytics dashboard']
    }
  }

  const selectedPlan = plans[plan as keyof typeof plans] || plans['premium-monthly']

  const handleCheckout = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement Stripe checkout session creation
      // This will integrate with your existing Stripe setup
      console.log('Creating checkout session for plan:', plan)

      // For now, simulate the checkout process
      alert(`Checkout for ${selectedPlan.name} - $${selectedPlan.price}/${selectedPlan.interval}`)

      // In a real implementation, you would:
      // 1. Create a Stripe checkout session
      // 2. Redirect to Stripe checkout
      // 3. Handle success/cancel redirects

    } catch (error) {
      console.error('Checkout error:', error)
      alert('Error starting checkout process')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
          <p className="text-gray-600 mt-2">Complete your subscription to {selectedPlan.name}</p>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Order Summary</h2>

          <div className="border border-gray-200 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{selectedPlan.name}</h3>
                <p className="text-sm text-gray-500">Billed {selectedPlan.interval}ly</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">
                  ${selectedPlan.price}
                </p>
                <p className="text-sm text-gray-500">per {selectedPlan.interval}</p>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Included features:</h4>
              <ul className="space-y-1">
                {selectedPlan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <span className="text-lg font-medium text-gray-900">Total</span>
              <span className="text-2xl font-bold text-gray-900">
                ${selectedPlan.price}/{selectedPlan.interval}
              </span>
            </div>

            <button
              onClick={handleCheckout}
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Processing...' : `Subscribe to ${selectedPlan.name}`}
            </button>

            <p className="text-xs text-gray-500 mt-4 text-center">
              You can cancel your subscription at any time. No long-term commitments.
            </p>
          </div>
        </div>

        <div className="text-center">
          <a
            href="/billing"
            className="text-blue-600 hover:text-blue-700 text-sm"
          >
            ← Back to subscription management
          </a>
        </div>
      </div>
    </div>
  )
}
